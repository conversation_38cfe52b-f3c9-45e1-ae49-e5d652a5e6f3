<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <meta name="description" content="AI-powered search engine for efficient and intelligent searching">
  <meta name="theme-color" content="#4285f4">
  <title>AI Search - 智能搜索引擎</title>
  <link rel="preconnect" href="https://www.google.com">
  <style>
    * {
      box-sizing: border-box;
    }
    
    body {
      margin: 0;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Arial, sans-serif;
      background: #fff;
      color: #202124;
    }
    
    .container {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      min-height: 100vh;
      padding: 20px;
      max-width: 600px;
      margin: 0 auto;
    }
    
    .ad-banner {
      width: 100%;
      background: linear-gradient(135deg, #fffbe6 0%, #fff9c4 100%);
      border: 1px solid #f0c14b;
      border-radius: 12px;
      padding: 16px 24px;
      margin-bottom: 32px;
      color: #b7791f;
      font-size: 16px;
      font-weight: 500;
      text-align: center;
      box-shadow: 0 2px 8px rgba(255, 193, 7, 0.15);
      opacity: 0;
      transform: translateY(-10px);
      animation: slideInFade 0.6s ease-out 0.3s forwards;
    }
    
    @keyframes slideInFade {
      to {
        opacity: 1;
        transform: translateY(0);
      }
    }
    
    .logo {
      width: 272px;
      height: 92px;
      margin-bottom: 32px;
      opacity: 0;
      animation: fadeIn 0.8s ease-out forwards;
    }
    
    @keyframes fadeIn {
      to { opacity: 1; }
    }
    
    .search-form {
      width: 100%;
      max-width: 584px;
    }
    
    .search-container {
      position: relative;
      display: flex;
      align-items: center;
      border: 1px solid #dfe1e5;
      border-radius: 24px;
      padding: 12px 16px;
      background: #fff;
      box-shadow: 0 2px 5px 1px rgba(64, 60, 67, 0.16);
      transition: box-shadow 0.3s ease, border-color 0.3s ease;
    }
    
    .search-container:hover {
      box-shadow: 0 2px 8px 1px rgba(64, 60, 67, 0.24);
    }
    
    .search-container:focus-within {
      border-color: #4285f4;
      box-shadow: 0 2px 8px 1px rgba(66, 133, 244, 0.24);
    }
    
    .search-input {
      flex: 1;
      border: none;
      outline: none;
      font-size: 16px;
      background: transparent;
      color: #202124;
      padding: 0;
    }
    
    .search-input::placeholder {
      color: #9aa0a6;
    }
    
    .button-container {
      display: flex;
      justify-content: center;
      gap: 12px;
      margin-top: 30px;
    }
    
    .search-button {
      background: #f8f9fa;
      border: 1px solid #f8f9fa;
      border-radius: 4px;
      padding: 10px 20px;
      font-size: 14px;
      color: #3c4043;
      cursor: pointer;
      transition: all 0.15s ease;
      font-family: inherit;
    }
    
    .search-button:hover {
      box-shadow: 0 1px 1px rgba(0, 0, 0, 0.1);
      background: #f1f3f4;
      border-color: #dadce0;
    }
    
    .search-button:focus {
      outline: 2px solid #4285f4;
      outline-offset: 2px;
    }
    
    .search-button:active {
      background: #e8eaed;
    }
    
    @media (max-width: 600px) {
      .container {
        padding: 16px;
        min-height: 80vh;
      }
      
      .logo {
        width: 200px;
        height: 68px;
        margin-bottom: 24px;
      }
      
      .ad-banner {
        font-size: 14px;
        padding: 12px 16px;
        margin-bottom: 24px;
      }
      
      .button-container {
        flex-direction: column;
        align-items: center;
        gap: 8px;
      }
      
      .search-button {
        width: 100%;
        max-width: 200px;
      }
    }
    
    @media (prefers-reduced-motion: reduce) {
      .ad-banner,
      .logo {
        animation: none;
        opacity: 1;
        transform: none;
      }
      
      .search-container,
      .search-button {
        transition: none;
      }
    }
  </style>
</head>
<body>
  <main class="container">
    <div class="ad-banner" role="banner" aria-label="AI搜索广告">
      AI赋能未来，搜索更高效，生活更智能！
    </div>
    
    <img 
      src="https://www.google.com/images/branding/googlelogo/2x/googlelogo_light_color_272x92dp.png" 
      alt="Google搜索引擎标志" 
      class="logo"
      loading="eager"
    >
    
    <form 
      action="https://www.google.com/search" 
      method="GET" 
      class="search-form"
      role="search"
      aria-label="搜索表单"
    >
      <div class="search-container">
        <input 
          type="text" 
          name="q" 
          class="search-input"
          placeholder="搜索或输入网址"
          autofocus 
          autocomplete="off"
          aria-label="搜索查询"
          maxlength="2048"
        >
      </div>
      
      <div class="button-container">
        <button 
          type="submit" 
          class="search-button"
          aria-label="执行Google搜索"
        >
          Google 搜索
        </button>
        <button 
          type="submit" 
          name="btnI" 
          value="1" 
          class="search-button"
          aria-label="手气不错，直接访问第一个搜索结果"
        >
          手气不错
        </button>
      </div>
    </form>
  </main>
</body>
</html>
